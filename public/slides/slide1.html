<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 1: Title</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #128C7E;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
            --yellow: #FFC107;
            --teal: #20C997;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: var(--white);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: var(--white);
            padding: 1.5rem;
            box-sizing: border-box;
        }

        .content-wrapper {
            text-align: center;
            max-width: 1200px;
            width: 100%;
            height: calc(100vh - 3rem);
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
        }

        .header-section {
            flex-shrink: 0;
        }

        .slide-badge {
            display: inline-block;
            padding: 12px 28px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-radius: 50px;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .logo-container {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 180px;
            height: 180px;
            background: radial-gradient(circle, rgba(37, 211, 102, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: pulse 3s ease-in-out infinite;
        }

        .logo-container img {
            height: 100px;
            filter: drop-shadow(0 8px 25px rgba(0,0,0,0.15));
            position: relative;
            z-index: 2;
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }

        .subtitle {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray);
            margin-bottom: 0.8rem;
        }

        .byline {
            font-size: 1.1rem;
            color: var(--gray);
            margin-bottom: 2rem;
            font-weight: 500;
        }

        .main-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-top: 2rem;
            max-height: 200px;
        }

        .stat-card {
            background: var(--white);
            padding: 1.8rem 1.5rem;
            border-radius: 20px;
            border: 3px solid transparent;
            background-clip: padding-box;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            height: 100%;
            max-height: 180px;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 3px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .stat-card:nth-child(1) {
            border-radius: 20px 20px 20px 5px;
        }

        .stat-card:nth-child(2) {
            border-radius: 5px 20px 20px 20px;
            transform: translateY(-10px);
        }

        .stat-card:nth-child(3) {
            border-radius: 20px 5px 20px 20px;
        }

        .stat-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 25px 50px rgba(37, 211, 102, 0.25);
        }

        .stat-card:nth-child(2):hover {
            transform: translateY(-25px) scale(1.05);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            display: block;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .stat-card:nth-child(1) .stat-number {
            background: linear-gradient(135deg, var(--blue), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card:nth-child(2) .stat-number {
            background: linear-gradient(135deg, var(--primary), var(--teal));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-card:nth-child(3) .stat-number {
            background: linear-gradient(135deg, var(--orange), var(--red));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--gray);
            font-weight: 600;
            line-height: 1.3;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 1.2rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            font-size: 1.1rem;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, var(--accent), var(--primary));
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 12px 30px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 1200px) {
            .slide-container {
                padding: 1rem;
            }

            .content-wrapper {
                height: calc(100vh - 2rem);
            }

            h1 {
                font-size: 3rem;
            }

            .subtitle {
                font-size: 1.3rem;
            }

            .byline {
                font-size: 1rem;
            }

            .stats-grid {
                gap: 1.2rem;
                max-height: 160px;
            }

            .stat-card {
                padding: 1.5rem 1.2rem;
                max-height: 150px;
            }

            .stat-number {
                font-size: 2.2rem;
            }

            .stat-label {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 768px) {
            .slide-container {
                padding: 0.8rem;
            }

            .content-wrapper {
                height: calc(100vh - 1.6rem);
            }

            h1 {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.2rem;
            }

            .byline {
                font-size: 0.95rem;
                margin-bottom: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                max-height: 300px;
            }

            .stat-card {
                padding: 1.2rem;
                border-radius: 15px;
                transform: none !important;
                max-height: 90px;
                min-height: 90px;
            }

            .stat-card:nth-child(2) {
                transform: none !important;
            }

            .stat-number {
                font-size: 2rem;
                margin-bottom: 0.3rem;
            }

            .stat-label {
                font-size: 0.9rem;
            }

            .logo-container img {
                height: 80px;
            }

            .navigation {
                bottom: 0.8rem;
                right: 0.8rem;
            }

            .nav-btn {
                padding: 0.8rem;
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        .slide-up {
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scale-in {
            animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bounce-in {
            animation: bounceIn 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(60px) rotateX(15deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8) rotateY(10deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3) rotateY(180deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.05) rotateY(90deg);
            }
            70% {
                transform: scale(0.9) rotateY(45deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.1);
                opacity: 0.1;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="header-section">
                <div class="slide-badge scale-in" style="animation-delay: 0.1s;">Investor Presentation 2024</div>

                <div class="logo-container bounce-in" style="animation-delay: 0.2s;">
                    <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                </div>

                <h1 class="slide-up" style="animation-delay: 0.3s;">Whamart</h1>
                <div class="subtitle slide-up" style="animation-delay: 0.4s;">Empowering Bharat's Local Businesses Digitally</div>
                <div class="byline slide-up" style="animation-delay: 0.5s;">Presented by Karan Solanki – Founder, Whamart</div>
            </div>

            <div class="main-section">
                <div class="stats-grid">
                    <div class="stat-card scale-in" style="animation-delay: 0.6s;">
                        <span class="stat-number">8Cr+</span>
                        <span class="stat-label">Target Businesses</span>
                    </div>
                    <div class="stat-card scale-in" style="animation-delay: 0.7s;">
                        <span class="stat-number">₹10L</span>
                        <span class="stat-label">Funding Goal</span>
                    </div>
                    <div class="stat-card scale-in" style="animation-delay: 0.8s;">
                        <span class="stat-number">30%+</span>
                        <span class="stat-label">Expected ROI</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" disabled>
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide2.html';
        }
        
        function previousSlide() {
            // First slide, no previous
        }
    </script>
</body>
</html>
