<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 3: Our Solution</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #128C7E;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
            --yellow: #FFC107;
            --teal: #20C997;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: var(--white);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: var(--white);
            padding: 1.5rem;
            box-sizing: border-box;
        }

        .content-wrapper {
            max-width: 1400px;
            width: 100%;
            height: calc(100vh - 3rem);
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
        }

        .header-section {
            text-align: center;
            margin-bottom: 1.5rem;
            flex-shrink: 0;
        }

        .slide-badge {
            display: inline-block;
            padding: 12px 28px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-radius: 50px;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }

        .main-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            height: 400px;
            max-height: 400px;
        }

        .feature-section {
            background: var(--white);
            padding: 1.8rem 1.5rem;
            border-radius: 20px;
            border: 3px solid transparent;
            background-clip: padding-box;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            height: 100%;
        }

        .feature-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 3px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .feature-section:nth-child(1) {
            border-radius: 20px 20px 20px 5px;
        }

        .feature-section:nth-child(2) {
            border-radius: 5px 20px 20px 20px;
            transform: translateY(-10px);
        }

        .feature-section:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(37, 211, 102, 0.25);
        }

        .feature-section:nth-child(2):hover {
            transform: translateY(-25px) scale(1.02);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 1.2rem;
            text-align: center;
            position: relative;
        }

        .section-title:nth-of-type(1) {
            background: linear-gradient(135deg, var(--primary), var(--teal));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title.blue {
            background: linear-gradient(135deg, var(--blue), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            flex: 1;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(37, 211, 102, 0.1);
        }

        .feature-item:hover {
            transform: translateX(8px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.15);
            background: rgba(255, 255, 255, 0.9);
        }

        .feature-icon {
            width: 2.2rem;
            height: 2.2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: white;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .feature-section:nth-child(1) .feature-icon {
            background: linear-gradient(135deg, var(--primary), var(--teal));
        }

        .feature-section:nth-child(2) .feature-icon {
            background: linear-gradient(135deg, var(--blue), var(--purple));
        }

        .feature-text {
            color: var(--dark);
            font-weight: 500;
            font-size: 0.9rem;
            line-height: 1.3;
            flex: 1;
        }

        .footer-section {
            flex-shrink: 0;
        }

        .footer-note {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(18, 140, 126, 0.1));
            border: 2px solid transparent;
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(37, 211, 102, 0.15);
            max-height: 80px;
        }

        .footer-note::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .footer-note::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(37, 211, 102, 0.05) 0%, transparent 70%);
            animation: pulse 3s ease-in-out infinite;
        }

        .footer-content {
            position: relative;
            z-index: 2;
        }

        .footer-note p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            font-weight: 600;
            line-height: 1.4;
        }

        .footer-note i {
            color: var(--orange);
            margin: 0 0.5rem;
            animation: bounce 2s ease-in-out infinite;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 1.2rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            font-size: 1.1rem;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, var(--accent), var(--primary));
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 12px 30px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 1200px) {
            .slide-container {
                padding: 1rem;
            }

            .content-wrapper {
                height: calc(100vh - 2rem);
            }

            h2 {
                font-size: 2.2rem;
                margin-bottom: 1.2rem;
            }

            .features-grid {
                height: 350px;
                gap: 1.2rem;
            }

            .feature-section {
                padding: 1.5rem 1.2rem;
            }

            .section-title {
                font-size: 1.2rem;
                margin-bottom: 1rem;
            }

            .feature-icon {
                width: 2rem;
                height: 2rem;
                font-size: 0.85rem;
            }

            .feature-text {
                font-size: 0.85rem;
            }

            .footer-note {
                padding: 1.2rem;
                max-height: 70px;
            }

            .footer-note p {
                font-size: 1rem;
            }
        }

        @media (max-width: 768px) {
            .slide-container {
                padding: 0.8rem;
            }

            .content-wrapper {
                height: calc(100vh - 1.6rem);
            }

            h2 {
                font-size: 1.8rem;
                margin-bottom: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                height: auto;
                max-height: 450px;
            }

            .feature-section {
                padding: 1.2rem;
                border-radius: 15px;
                transform: none !important;
                max-height: 220px;
            }

            .feature-section:nth-child(2) {
                transform: none !important;
            }

            .section-title {
                font-size: 1.1rem;
                margin-bottom: 0.8rem;
            }

            .feature-list {
                gap: 0.6rem;
            }

            .feature-item {
                padding: 0.6rem;
                border-radius: 10px;
            }

            .feature-icon {
                width: 1.8rem;
                height: 1.8rem;
                font-size: 0.8rem;
                margin-right: 0.6rem;
            }

            .feature-text {
                font-size: 0.8rem;
                line-height: 1.2;
            }

            .footer-note {
                padding: 1rem;
                border-radius: 15px;
                max-height: 60px;
            }

            .footer-note p {
                font-size: 0.9rem;
                line-height: 1.3;
            }

            .footer-note i {
                font-size: 0.9rem;
                margin: 0 0.3rem;
            }

            .navigation {
                bottom: 0.8rem;
                right: 0.8rem;
            }

            .nav-btn {
                padding: 0.8rem;
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        .slide-up {
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scale-in {
            animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(60px) rotateX(15deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8) rotateY(10deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.1;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-3px);
            }
            60% {
                transform: translateY(-2px);
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="header-section">
                <div class="slide-badge scale-in" style="animation-delay: 0.1s;">Our Solution</div>
                <h2 class="slide-up" style="animation-delay: 0.2s;">Whamart — One Simple Solution for All Local Business Needs</h2>
            </div>

            <div class="main-section">
                <div class="features-grid">
                    <div class="feature-section slide-up" style="animation-delay: 0.3s;">
                        <h3 class="section-title">Key Features</h3>
                        <div class="feature-list">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-store"></i>
                                </div>
                                <span class="feature-text">WhatsApp-style mini-store for products & services</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <span class="feature-text">Drag-and-drop chatbot builder for auto-replies</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <span class="feature-text">Product catalog with pricing, images & details</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <span class="feature-text">PDF bill generation for every customer</span>
                            </div>
                        </div>
                    </div>

                    <div class="feature-section slide-up" style="animation-delay: 0.4s;">
                        <h3 class="section-title blue">Advanced Benefits</h3>
                        <div class="feature-list">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <span class="feature-text">Verified blue tick for all stores</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <span class="feature-text">24x7 automation — runs even when owner is offline</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-mobile-screen"></i>
                                </div>
                                <span class="feature-text">No third-party app required to access the store</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-rupee-sign"></i>
                                </div>
                                <span class="feature-text">Accept direct UPI payments (GPay, PhonePe, Paytm) — No gateway fee</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer-section">
                    <div class="footer-note scale-in" style="animation-delay: 0.5s;">
                        <div class="footer-content">
                            <p>
                                <i class="fas fa-quote-left"></i>
                                <strong>Whamart makes even a chaiwala or tutor look like a pro brand.</strong>
                                <i class="fas fa-quote-right"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide4.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide2.html';
        }
    </script>
</body>
</html>
