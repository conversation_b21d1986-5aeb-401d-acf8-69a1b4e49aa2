<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 11: Thank You</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #E9F7EF;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--blue) 100%);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }


        .content-wrapper {
            max-width: 1200px;
            width: 100%;
            padding: 1.5rem 1rem;
            position: relative;
            z-index: 1;
        }

        .slide-badge {
            display: inline-block;
            padding: 6px 16px;
            background: rgba(37, 211, 102, 0.1);
            color: var(--primary);
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 1rem;
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        h1 {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, var(--blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
            text-align: center;
        }

        .investment-summary {
            margin-bottom: 1.5rem;
        }

        .summary-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .summary-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .summary-text {
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
            line-height: 1.3;
        }

        .contact-section {
            margin-bottom: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-gray);
            border-radius: 0.5rem;
            border: 1px solid rgba(37, 211, 102, 0.1);
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(37, 211, 102, 0.15);
        }

        .contact-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            min-width: 30px;
        }

        .contact-label {
            font-size: 0.9rem;
            color: var(--gray);
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .contact-value {
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 600;
        }

        .thank-you-box {
            background: rgba(37, 211, 102, 0.1);
            border: 1px solid rgba(37, 211, 102, 0.2);
            border-radius: 0.75rem;
            padding: 1.25rem;
            text-align: center;
        }

        .thank-you-box p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
            font-style: italic;
            line-height: 1.4;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .nav-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                padding: 1rem 0.75rem;
            }

            h1 {
                font-size: 1.6rem;
                margin-bottom: 1rem;
            }

            .investment-summary,
            .contact-section {
                margin-bottom: 1rem;
            }

            .summary-item,
            .contact-item {
                padding: 0.5rem;
            }

            .summary-text,
            .contact-label,
            .contact-value {
                font-size: 0.8rem;
            }

            .thank-you-box {
                padding: 1rem;
            }

            .thank-you-box p {
                font-size: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        .bounce-in {
            animation: bounceIn 1.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="slide-badge">Final Call</div>

            <h1>Let's Build the Future of Local Commerce</h1>

            <div class="investment-summary">
                <div class="summary-item">
                    <span class="summary-icon">📌</span>
                    <span class="summary-text">We're raising ₹10L to digitally empower India's small businesses</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📌</span>
                    <span class="summary-text">Capital will be used for customer acquisition, infra, and marketing</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📌</span>
                    <span class="summary-text">You can join at ₹5L or more</span>
                </div>
                <div class="summary-item">
                    <span class="summary-icon">📌</span>
                    <span class="summary-text">Returns up to 40% in 6 months with monthly reports</span>
                </div>
            </div>

            <div class="contact-section">
                <div class="contact-item">
                    <span class="contact-icon">📧</span>
                    <span class="contact-label">Contact:</span>
                    <span class="contact-value"><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span class="contact-icon">📱</span>
                    <span class="contact-label">Phone / WhatsApp:</span>
                    <span class="contact-value">[Your Number]</span>
                </div>
                <div class="contact-item">
                    <span class="contact-icon">🌐</span>
                    <span class="contact-label">Demo:</span>
                    <span class="contact-value">[Link to your site/trial]</span>
                </div>
            </div>

            <div class="thank-you-box">
                <p>Thank you for your time & belief in our mission.</p>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" disabled>
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            // Last slide, no next
        }
        
        function previousSlide() {
            window.location.href = 'slide10.html';
        }
    </script>
</body>
</html>
