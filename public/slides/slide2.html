<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 2: Problem Statement</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #128C7E;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
            --yellow: #FFC107;
            --teal: #20C997;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: var(--white);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: var(--white);
            padding: 1.5rem;
            box-sizing: border-box;
            overflow: hidden;
        }

        .content-wrapper {
            max-width: 1300px;
            width: 100%;
            height: calc(100vh - 3rem);
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            box-sizing: border-box;
        }

        .header-section {
            text-align: center;
            margin-bottom: 1.5rem;
            flex-shrink: 0;
        }

        .slide-badge {
            display: inline-block;
            padding: 8px 20px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-radius: 25px;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }

        .subtitle {
            font-size: 1.1rem;
            color: var(--gray);
            font-weight: 500;
            margin-bottom: 1.5rem;
        }

        .problems-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 0;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 1rem;
            height: 60%;
            margin-bottom: 1rem;
        }

        .problem-card {
            background: var(--white);
            padding: 1.2rem;
            border-radius: 15px;
            border: 2px solid transparent;
            background-clip: padding-box;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: left;
            display: flex;
            flex-direction: row;
            align-items: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            height: 100%;
            box-sizing: border-box;
        }

        .problem-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 15px;
            padding: 2px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        /* PowerPoint-style card variations */
        .problem-card:nth-child(1) {
            border-radius: 15px 15px 15px 3px;
            grid-column: 1;
            grid-row: 1;
        }

        .problem-card:nth-child(2) {
            border-radius: 3px 15px 15px 15px;
            grid-column: 2;
            grid-row: 1;
        }

        .problem-card:nth-child(3) {
            border-radius: 15px 3px 15px 15px;
            grid-column: 1;
            grid-row: 2;
        }

        .problem-card:nth-child(4) {
            border-radius: 15px 15px 3px 15px;
            grid-column: 2;
            grid-row: 2;
        }

        .problem-card:nth-child(5) {
            border-radius: 3px 15px 15px 15px;
            grid-column: 1 / -1;
            grid-row: 3;
            flex-direction: row;
            justify-content: center;
            text-align: center;
        }

        .problem-card:hover {
            transform: translateY(-3px) scale(1.01);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.15);
        }

        .problem-icon-container {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            position: relative;
        }

        .problem-card:nth-child(5) .problem-icon-container {
            margin-right: 1.5rem;
        }

        .problem-icon {
            font-size: 2rem;
            display: block;
            position: relative;
        }

        .problem-card:nth-child(1) .problem-icon-container {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(253, 126, 20, 0.1));
        }

        .problem-card:nth-child(1) .problem-icon {
            background: linear-gradient(135deg, var(--red), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(2) .problem-icon-container {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(111, 66, 193, 0.1));
        }

        .problem-card:nth-child(2) .problem-icon {
            background: linear-gradient(135deg, var(--blue), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(3) .problem-icon-container {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(32, 201, 151, 0.1));
        }

        .problem-card:nth-child(3) .problem-icon {
            background: linear-gradient(135deg, var(--primary), var(--teal));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(4) .problem-icon-container {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
        }

        .problem-card:nth-child(4) .problem-icon {
            background: linear-gradient(135deg, var(--yellow), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(5) .problem-icon-container {
            background: linear-gradient(135deg, rgba(111, 66, 193, 0.1), rgba(7, 94, 84, 0.1));
        }

        .problem-card:nth-child(5) .problem-icon {
            background: linear-gradient(135deg, var(--purple), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-content {
            flex: 1;
            min-width: 0;
        }

        .problem-title {
            font-size: 1rem;
            font-weight: 700;
            margin-bottom: 0.4rem;
            color: var(--dark);
            line-height: 1.2;
        }

        .problem-text {
            font-size: 0.85rem;
            color: var(--gray);
            line-height: 1.4;
            font-weight: 500;
        }

        .opportunity-section {
            height: 35%;
            display: flex;
            align-items: center;
        }

        .opportunity-box {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.08), rgba(18, 140, 126, 0.08));
            border: 2px solid transparent;
            border-radius: 18px;
            padding: 1.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.12);
            width: 100%;
            box-sizing: border-box;
        }

        .opportunity-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 18px;
            padding: 2px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .opportunity-box::after {
            content: '';
            position: absolute;
            top: -30%;
            left: -30%;
            width: 160%;
            height: 160%;
            background: radial-gradient(circle, rgba(37, 211, 102, 0.03) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        .opportunity-content {
            position: relative;
            z-index: 2;
        }

        .opportunity-box p {
            font-size: 1.1rem;
            color: var(--dark);
            margin: 0;
            line-height: 1.5;
            font-weight: 600;
        }

        .opportunity-box i {
            color: var(--orange);
            margin-right: 0.5rem;
            font-size: 1.2rem;
            animation: bounce 2s ease-in-out infinite;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 1.2rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            font-size: 1.1rem;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, var(--accent), var(--primary));
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 12px 30px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 1200px) {
            .slide-container {
                padding: 1.2rem;
            }

            h2 {
                font-size: 2.2rem;
            }

            .problems-grid {
                height: 55%;
                gap: 0.8rem;
            }

            .problem-card {
                padding: 1rem;
            }

            .problem-icon-container {
                width: 50px;
                height: 50px;
                margin-right: 0.8rem;
            }

            .problem-icon {
                font-size: 1.8rem;
            }

            .problem-title {
                font-size: 0.95rem;
            }

            .problem-text {
                font-size: 0.8rem;
            }

            .opportunity-box p {
                font-size: 1rem;
            }
        }

        @media (max-width: 768px) {
            .slide-container {
                padding: 1rem;
            }

            .content-wrapper {
                height: calc(100vh - 2rem);
            }

            h2 {
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }

            .subtitle {
                font-size: 1rem;
                margin-bottom: 1rem;
            }

            .problems-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, auto);
                height: auto;
                gap: 0.8rem;
                margin-bottom: 1rem;
            }

            .problem-card {
                padding: 1rem;
                border-radius: 12px;
                flex-direction: row;
                text-align: left;
            }

            .problem-card:nth-child(5) {
                grid-column: 1;
                flex-direction: row;
                text-align: left;
            }

            .problem-icon-container {
                width: 45px;
                height: 45px;
                margin-right: 0.8rem;
            }

            .problem-card:nth-child(5) .problem-icon-container {
                margin-right: 0.8rem;
            }

            .problem-icon {
                font-size: 1.5rem;
            }

            .problem-title {
                font-size: 0.9rem;
                margin-bottom: 0.3rem;
            }

            .problem-text {
                font-size: 0.75rem;
                line-height: 1.3;
            }

            .opportunity-section {
                height: auto;
                margin-top: 1rem;
            }

            .opportunity-box {
                padding: 1.2rem 1.5rem;
                border-radius: 15px;
            }

            .opportunity-box p {
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .opportunity-box i {
                font-size: 1rem;
            }

            .navigation {
                bottom: 0.8rem;
                right: 0.8rem;
            }

            .nav-btn {
                padding: 0.8rem;
                font-size: 0.9rem;
            }
        }

        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        .slide-up {
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scale-in {
            animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(60px) rotateX(15deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8) rotateY(10deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.1;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="header-section">
                <div class="slide-badge scale-in" style="animation-delay: 0.1s;">Problem Statement</div>
                <h2 class="slide-up" style="animation-delay: 0.2s;">The Problem We're Solving</h2>
                <div class="subtitle slide-up" style="animation-delay: 0.3s;">Challenges facing 8 crore+ small businesses in India</div>
            </div>

            <div class="problems-section">
                <div class="problems-grid">
                    <div class="problem-card slide-up" style="animation-delay: 0.4s;">
                        <div class="problem-icon-container">
                            <i class="fas fa-shop-slash problem-icon"></i>
                        </div>
                        <div class="problem-content">
                            <div class="problem-title">Digital Struggle</div>
                            <div class="problem-text">8 crore+ small businesses in India still struggle to go digital</div>
                        </div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.5s;">
                        <div class="problem-icon-container">
                            <i class="fas fa-puzzle-piece problem-icon"></i>
                        </div>
                        <div class="problem-content">
                            <div class="problem-title">Costly Platforms</div>
                            <div class="problem-text">Ecommerce platforms are costly, technical, and hard to manage</div>
                        </div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.6s;">
                        <div class="problem-icon-container">
                            <i class="fab fa-whatsapp problem-icon"></i>
                        </div>
                        <div class="problem-content">
                            <div class="problem-title">Expensive WhatsApp API</div>
                            <div class="problem-text">WhatsApp Business API is expensive and difficult for micro vendors</div>
                        </div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.7s;">
                        <div class="problem-icon-container">
                            <i class="fas fa-credit-card problem-icon"></i>
                        </div>
                        <div class="problem-content">
                            <div class="problem-title">Payment Gateway Issues</div>
                            <div class="problem-text">Payment gateways require KYC, setup time, and deduct commission</div>
                        </div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.8s;">
                        <div class="problem-icon-container">
                            <i class="fas fa-shield-alt problem-icon"></i>
                        </div>
                        <div class="problem-content">
                            <div class="problem-title">Trust Building Tools</div>
                            <div class="problem-text">Small vendors lack tools to build trust (verified identity, invoice, etc.)</div>
                        </div>
                    </div>
                </div>

                <div class="opportunity-section">
                    <div class="opportunity-box scale-in" style="animation-delay: 0.9s;">
                        <div class="opportunity-content">
                            <p>
                                <i class="fas fa-lightbulb"></i>
                                <strong>The Opportunity:</strong> 8 crore+ small businesses need a simple, affordable, and trustworthy way to go digital and reach customers through WhatsApp.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide3.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide1.html';
        }
    </script>
</body>
</html>
