<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 2: Problem Statement</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --secondary: #128C7E;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --blue: #007BFF;
            --purple: #6F42C1;
            --orange: #FD7E14;
            --red: #DC3545;
            --yellow: #FFC107;
            --teal: #20C997;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background: var(--white);
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: var(--white);
            padding: 2rem;
        }

        .content-wrapper {
            max-width: 1400px;
            width: 100%;
            height: 100%;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .header-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .slide-badge {
            display: inline-block;
            padding: 12px 28px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: var(--white);
            border-radius: 50px;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        h2 {
            font-size: 3.2rem;
            font-weight: 800;
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }

        .subtitle {
            font-size: 1.3rem;
            color: var(--gray);
            font-weight: 500;
            margin-bottom: 2rem;
        }

        .problems-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
            height: 400px;
        }

        .problem-card {
            background: var(--white);
            padding: 2rem 1.5rem;
            border-radius: 20px;
            border: 3px solid transparent;
            background-clip: padding-box;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .problem-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 3px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .problem-card:nth-child(1) {
            border-radius: 20px 20px 20px 5px;
        }

        .problem-card:nth-child(2) {
            border-radius: 5px 20px 20px 20px;
            transform: translateY(-10px);
        }

        .problem-card:nth-child(3) {
            border-radius: 20px 5px 20px 20px;
        }

        .problem-card:nth-child(4) {
            border-radius: 20px 20px 5px 20px;
            transform: translateY(-10px);
        }

        .problem-card:nth-child(5) {
            border-radius: 5px 20px 20px 20px;
            grid-column: 2;
        }

        .problem-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(37, 211, 102, 0.25);
        }

        .problem-card:nth-child(2):hover,
        .problem-card:nth-child(4):hover {
            transform: translateY(-25px) scale(1.02);
        }

        .problem-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            display: block;
            position: relative;
        }

        .problem-card:nth-child(1) .problem-icon {
            background: linear-gradient(135deg, var(--red), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(2) .problem-icon {
            background: linear-gradient(135deg, var(--blue), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(3) .problem-icon {
            background: linear-gradient(135deg, var(--primary), var(--teal));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(4) .problem-icon {
            background: linear-gradient(135deg, var(--yellow), var(--orange));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-card:nth-child(5) .problem-icon {
            background: linear-gradient(135deg, var(--purple), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .problem-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--dark);
            line-height: 1.2;
        }

        .problem-text {
            font-size: 1rem;
            color: var(--gray);
            line-height: 1.5;
            font-weight: 500;
        }

        .opportunity-section {
            margin-top: 2rem;
        }

        .opportunity-box {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1), rgba(18, 140, 126, 0.1));
            border: 3px solid transparent;
            border-radius: 25px;
            padding: 2.5rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(37, 211, 102, 0.15);
        }

        .opportunity-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 25px;
            padding: 3px;
            background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
        }

        .opportunity-box::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(37, 211, 102, 0.05) 0%, transparent 70%);
            animation: pulse 3s ease-in-out infinite;
        }

        .opportunity-content {
            position: relative;
            z-index: 2;
        }

        .opportunity-box p {
            font-size: 1.4rem;
            color: var(--dark);
            margin: 0;
            line-height: 1.6;
            font-weight: 600;
        }

        .opportunity-box i {
            color: var(--orange);
            margin-right: 0.75rem;
            font-size: 1.6rem;
            animation: bounce 2s ease-in-out infinite;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 1.2rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
            font-size: 1.1rem;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, var(--accent), var(--primary));
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 12px 30px rgba(37, 211, 102, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media (max-width: 1200px) {
            .slide-container {
                padding: 1.5rem;
            }

            h2 {
                font-size: 2.8rem;
            }

            .problems-grid {
                height: 350px;
                gap: 1.2rem;
            }

            .problem-card {
                padding: 1.5rem 1.2rem;
            }

            .problem-icon {
                font-size: 3rem;
            }

            .problem-title {
                font-size: 1.2rem;
            }

            .problem-text {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 768px) {
            .slide-container {
                padding: 1rem;
            }

            h2 {
                font-size: 2.2rem;
                margin-bottom: 1rem;
            }

            .subtitle {
                font-size: 1.1rem;
                margin-bottom: 1.5rem;
            }

            .problems-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(5, 1fr);
                height: auto;
                gap: 1rem;
            }

            .problem-card {
                padding: 1.5rem;
                border-radius: 15px;
                transform: none !important;
            }

            .problem-card:nth-child(2),
            .problem-card:nth-child(4) {
                transform: none !important;
            }

            .problem-card:nth-child(5) {
                grid-column: 1;
            }

            .problem-icon {
                font-size: 2.5rem;
            }

            .problem-title {
                font-size: 1.1rem;
            }

            .problem-text {
                font-size: 0.9rem;
            }

            .opportunity-box {
                padding: 2rem 1.5rem;
                border-radius: 20px;
            }

            .opportunity-box p {
                font-size: 1.2rem;
            }

            .navigation {
                bottom: 1rem;
                right: 1rem;
            }

            .nav-btn {
                padding: 1rem;
                font-size: 1rem;
            }
        }

        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        .slide-up {
            animation: slideUp 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scale-in {
            animation: scaleIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(40px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(60px) rotateX(15deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8) rotateY(10deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 0.3;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.1;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="content-wrapper fade-in">
            <div class="header-section">
                <div class="slide-badge scale-in" style="animation-delay: 0.1s;">Problem Statement</div>
                <h2 class="slide-up" style="animation-delay: 0.2s;">The Problem We're Solving</h2>
                <div class="subtitle slide-up" style="animation-delay: 0.3s;">Challenges facing 8 crore+ small businesses in India</div>
            </div>

            <div class="problems-section">
                <div class="problems-grid">
                    <div class="problem-card slide-up" style="animation-delay: 0.4s;">
                        <i class="fas fa-shop-slash problem-icon"></i>
                        <div class="problem-title">Digital Struggle</div>
                        <div class="problem-text">8 crore+ small businesses in India still struggle to go digital</div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.5s;">
                        <i class="fas fa-puzzle-piece problem-icon"></i>
                        <div class="problem-title">Costly Platforms</div>
                        <div class="problem-text">Ecommerce platforms are costly, technical, and hard to manage</div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.6s;">
                        <i class="fab fa-whatsapp problem-icon"></i>
                        <div class="problem-title">Expensive WhatsApp API</div>
                        <div class="problem-text">WhatsApp Business API is expensive and difficult for micro vendors</div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.7s;">
                        <i class="fas fa-credit-card problem-icon"></i>
                        <div class="problem-title">Payment Gateway Issues</div>
                        <div class="problem-text">Payment gateways require KYC, setup time, and deduct commission</div>
                    </div>

                    <div class="problem-card slide-up" style="animation-delay: 0.8s;">
                        <i class="fas fa-shield-alt problem-icon"></i>
                        <div class="problem-title">Trust Building Tools</div>
                        <div class="problem-text">Small vendors lack tools to build trust (verified identity, invoice, etc.)</div>
                    </div>
                </div>

                <div class="opportunity-section">
                    <div class="opportunity-box scale-in" style="animation-delay: 0.9s;">
                        <div class="opportunity-content">
                            <p>
                                <i class="fas fa-lightbulb"></i>
                                <strong>The Opportunity:</strong> 8 crore+ small businesses need a simple, affordable, and trustworthy way to go digital and reach customers through WhatsApp.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide3.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide1.html';
        }
    </script>
</body>
</html>
